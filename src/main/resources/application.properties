quarkus.banner.enabled=false
quarkus.http.port=8080

quarkus.profile=dev
tenant=yolo
base-url=https://${tenant}-api.${quarkus.profile}.yoloassicurazioni.it

# Configure default tenant datasource
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=megauser
quarkus.datasource.password=W5ZDxien5zuNskAQef8Mnz96gsdMHHo
quarkus.datasource.jdbc.url=***************************************************************************************
quarkus.datasource.jdbc.driver=org.postgresql.Driver
quarkus.hibernate-orm.dialect=org.hibernate.dialect.PostgreSQLDialect
quarkus.hibernate-orm.jdbc.timezone=UTC
quarkus.hibernate-orm.log.sql=false
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
#CUSTOMER REST CLIENT
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=9024
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG

#PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=${base-url}/iad-product/
quarkus.rest-client.iad-product.scope=jakarta.enterprise.context.ApplicationScoped
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=${base-url}/iad-customer
quarkus.rest-client.iad-customer.scope=jakarta.enterprise.context.ApplicationScoped
#ORDER REST CLIENT
quarkus.rest-client.iad-order.url=${base-url}/iad-order
quarkus.rest-client.iad-order.scope=jakarta.enterprise.context.ApplicationScoped
#FILE-UPLOADER REST CLIENT
quarkus.rest-client.file-uploader.url=${base-url}/iad-file-uploader
quarkus.rest-client.file-uploader.scope=jakarta.enterprise.context.ApplicationScoped
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=http://127.0.0.1:3000
quarkus.rest-client.provider-gateway.scope=jakarta.enterprise.context.ApplicationScoped
quarkus.rest-client.provider-gateway.read-timeout= 60000
quarkus.rest-client.provider-gateway.connect-timeout= 60000

#GRPC CLIENTS
#TOKEN SERVICE
#GRPC CLIENT-TOKEN
quarkus.grpc.clients.tokenService.host=localhost
quarkus.grpc.clients.tokenService.port=9003
