package be.digitech.italianautility.service.v1;

import be.digitech.italianautility.client.FileUploaderClient;
import be.digitech.italianautility.client.PgwClient;
import be.digitech.italianautility.exception.ExternalServiceException;
import be.digitech.italianautility.service.v1.utilityService.JsonManipulationService;
import be.digitech.italianautility.service.v1.utilityService.ServiceOrder;
import be.digitech.italianautility.service.v1.utilityService.Utility;
import be.digitech.italianautility.service.v1.utilityService.ValidationService;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.ClientWebApplicationException;

import java.io.File;
import java.util.List;

@ApplicationScoped
public class ItalianaService {

    @Inject
    Logger logger;

    @Inject
    @RestClient
    PgwClient pgwClient;

    @Inject
    @RestClient
    FileUploaderClient fileUploaderClient;

    @Inject
    Utility utility;

    @Inject
    ServiceOrder orderService;

    @Inject
    ValidationService validationService;

    @Inject
    JsonManipulationService jsonManipulationService;

    /**
     * Genera il numero di proposta tramite il Provider Gateway
     */
    public JsonNode generazioneNumeroProposta(String orderCode, String token, String sessionId) {
        logger.infov("Inizio generazione numero proposta per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        // Preparazione richiesta al Provider Gateway
        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);

        // Chiamata al Provider Gateway
        JsonNode response = callProviderGateway(pgwRequest, "generazioneNumeroProposta");

        // Recupero e aggiornamento dell'ordine
        JsonNode order = orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode updatedOrder = jsonManipulationService.updateOrderWithProviderResponse(
            order, response, "numeroProposta");

        // Salvataggio dell'ordine aggiornato
        orderService.updateOrder(token, orderCode, updatedOrder);

        // Salvataggio della proposta
        salvataggioProposta(orderCode, token, sessionId);

        logger.infov("Generazione numero proposta completata con successo per orderCode: {0}", orderCode);
        return response;
    }


    /**
     * Salva la proposta generata tramite il Provider Gateway
     */
    public void salvataggioProposta(String orderCode, String token, String sessionId) {
        logger.infov("Inizio salvataggio proposta per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
        callProviderGateway(pgwRequest, "salvataggioPropostaGenerata");

        logger.infov("Salvataggio proposta completato con successo per orderCode: {0}", orderCode);
    }

    /**
     * Metodo helper per le chiamate al Provider Gateway con gestione errori centralizzata
     */
    private JsonNode callProviderGateway(ObjectNode request, String action) {
        try {
            logger.infov("Chiamata al Provider Gateway per azione: {0}", action);
            JsonNode response = pgwClient.action(request, action).readEntity(JsonNode.class);
            logger.infov("Chiamata al Provider Gateway completata con successo per azione: {0}", action);
            return response;

        } catch (ClientWebApplicationException e) {
            handleProviderGatewayError(e, action);
            return null; // Non raggiungerà mai questo punto
        } catch (Exception e) {
            logger.errorv(e, "Errore generico nella chiamata al Provider Gateway per azione: {0}", action);
            throw new ExternalServiceException("Provider Gateway",
                "Errore nella comunicazione con il Provider Gateway per azione: " + action, e);
        }
    }

    /**
     * Gestisce gli errori HTTP delle chiamate al Provider Gateway
     */
    private void handleProviderGatewayError(ClientWebApplicationException e, String action) {
        logger.errorv(e, "Errore HTTP nella chiamata al Provider Gateway per azione: {0}, status: {1}",
                     action, e.getResponse().getStatus());

        JsonNode providerErrorResponse = parseProviderErrorResponse(e);
        String errorMessage = extractErrorMessage(providerErrorResponse, action);
        JsonNode providerErrorDetails = extractErrorDetails(providerErrorResponse);

        throw new ExternalServiceException("Provider Gateway",
            errorMessage,
            e.getResponse().getStatus(),
            providerErrorDetails,
            e);
    }

    /**
     * Estrae la risposta di errore dal Provider Gateway
     */
    private JsonNode parseProviderErrorResponse(ClientWebApplicationException e) {
        try {
            JsonNode errorResponse = e.getResponse().readEntity(JsonNode.class);
            logger.infov("Dettagli errore dal Provider Gateway: {0}", errorResponse);
            return errorResponse;
        } catch (Exception parseException) {
            logger.warnv("Impossibile parsare la risposta di errore del Provider Gateway: {0}",
                        parseException.getMessage());
            return null;
        }
    }

    /**
     * Estrae il messaggio di errore dalla risposta del Provider Gateway
     */
    private String extractErrorMessage(JsonNode providerErrorResponse, String action) {
        if (providerErrorResponse == null) {
            return "Errore nella chiamata all'azione: " + action;
        }

        // Nuovo formato con messages.status e messages.response.text
        if (hasNestedResponseText(providerErrorResponse)) {
            return extractMessageFromNestedResponse(providerErrorResponse);
        }

        // Formato precedente con messages.message
        if (providerErrorResponse.has("messages")) {
            JsonNode messages = providerErrorResponse.get("messages");
            if (messages.has("message")) {
                return messages.get("message").asText();
            }
        }

        return "Errore nella chiamata all'azione: " + action;
    }

    /**
     * Verifica se la risposta ha la struttura messages.response.text
     */
    private boolean hasNestedResponseText(JsonNode providerErrorResponse) {
        return providerErrorResponse.has("messages") &&
               providerErrorResponse.get("messages").has("response") &&
               providerErrorResponse.get("messages").get("response").has("text");
    }

    /**
     * Estrae il messaggio di errore dalla struttura messages.response.text
     */
    private String extractMessageFromNestedResponse(JsonNode providerErrorResponse) {
        try {
            String textContent = providerErrorResponse.get("messages").get("response").get("text").asText();
            JsonNode parsedErrorContent = parseJsonText(textContent);

            return buildErrorMessageFromParsedContent(parsedErrorContent);

        } catch (Exception ex) {
            logger.warnv("Errore durante l'estrazione dei dettagli dell'errore: {0}", ex.getMessage());
            return "Errore durante l'elaborazione della risposta di errore";
        }
    }

    /**
     * Parsa il contenuto JSON se è una stringa JSON valida
     */
    private JsonNode parseJsonText(String textContent) {
        if (textContent.startsWith("{")) {
            try {
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                return mapper.readTree(textContent);
            } catch (Exception parseEx) {
                logger.warnv("Impossibile parsare il contenuto JSON del campo text: {0}", parseEx.getMessage());
            }
        }
        return null;
    }

    /**
     * Costruisce il messaggio di errore dai campi segnalazioni ed errori
     */
    private String buildErrorMessageFromParsedContent(JsonNode parsedErrorContent) {
        if (parsedErrorContent == null) {
            return "Errore nel parsing del contenuto di errore";
        }

        StringBuilder messageBuilder = new StringBuilder();

        if (parsedErrorContent.has("segnalazioni") && parsedErrorContent.get("segnalazioni").isArray() && !parsedErrorContent.get("segnalazioni").isEmpty()) {
            messageBuilder.append("Segnalazioni: ");
            parsedErrorContent.get("segnalazioni").forEach(segnalazione -> {
                if (segnalazione.has("descrizione")) {
                    messageBuilder.append(segnalazione.get("descrizione").asText()).append("; ");
                }
            });
        }

        if (parsedErrorContent.has("errori") && parsedErrorContent.get("errori").isArray() && !parsedErrorContent.get("errori").isEmpty()) {
            if (!messageBuilder.isEmpty()) messageBuilder.append(" - ");
            messageBuilder.append("Errori: ");
            parsedErrorContent.get("errori").forEach(errore -> {
                if (errore.has("descrizione")) {
                    messageBuilder.append(errore.get("descrizione").asText()).append("; ");
                }
            });
        }

        if (!messageBuilder.isEmpty()) {
            String result = messageBuilder.toString().trim();
            return result.endsWith(";") ? result.substring(0, result.length() - 1) : result;
        }

        return "Errore senza dettagli specifici";
    }

    /**
     * Estrae i dettagli completi dell'errore per includerli nella risposta
     */
    private JsonNode extractErrorDetails(JsonNode providerErrorResponse) {
        if (providerErrorResponse == null) {
            return null;
        }

        // Per il nuovo formato, estrai il contenuto parsato
        if (hasNestedResponseText(providerErrorResponse)) {
            try {
                String textContent = providerErrorResponse.get("messages").get("response").get("text").asText();
                JsonNode parsedContent = parseJsonText(textContent);
                if (parsedContent != null) {
                    return parsedContent;
                }
            } catch (Exception ex) {
                logger.warnv("Errore durante l'estrazione dei dettagli: {0}", ex.getMessage());
            }
        }

        // Per il formato precedente
        if (providerErrorResponse.has("messages")) {
            JsonNode messages = providerErrorResponse.get("messages");
            if (messages.has("details")) {
                return messages.get("details");
            }
        }

        // Fallback: usa l'intera risposta come dettagli
        return providerErrorResponse;
    }


    /**
     * Recupera i soggetti fisici tramite il Provider Gateway
     */
    public JsonNode soggettiFisici(String orderCode, String token, String sessionId) {
        logger.infov("Inizio recupero soggetti fisici per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        // Preparazione richiesta al Provider Gateway
        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);

        // Chiamata al Provider Gateway
        JsonNode response = callProviderGateway(pgwRequest, "soggettiFisici");

        // Recupero e aggiornamento dell'ordine
        JsonNode order = orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode updatedOrder = jsonManipulationService.updateOrderWithProviderResponse(
            order, response, "codiceNominativo");

        // Salvataggio dell'ordine aggiornato
        orderService.updateOrder(token, orderCode, updatedOrder);

        logger.infov("Recupero soggetti fisici completato con successo per orderCode: {0}", orderCode);
        return response;
    }

    /**
     * Recupera i documenti vita tramite il Provider Gateway
     * TODO: Implementare gestione documenti completa
     */
    public JsonNode vitaDocumenti(String orderCode, String token, String sessionId) {
        logger.infov("Inizio recupero documenti vita per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
        JsonNode response = callProviderGateway(pgwRequest, "vitaDocumenti");

        logger.infov("Recupero documenti vita completato per orderCode: {0}", orderCode);
        return response;
    }

    /**
     * Recupera il questionario di adeguatezza tramite il Provider Gateway
     */
    public JsonNode questionarioAdeguatezza(String orderCode, String token, String sessionId) {
        logger.infov("Inizio recupero questionario adeguatezza per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
        JsonNode response = callProviderGateway(pgwRequest, "questionarioAdeguatezza");

        logger.infov("Recupero questionario adeguatezza completato per orderCode: {0}", orderCode);
        return response;
    }

    /**
     * Invia il questionario di adeguatezza tramite il Provider Gateway
     */
    public JsonNode postQuestionarioAdeguatezza(String orderCode, String token, String sessionId, JsonNode req) {
        logger.infov("Inizio invio questionario adeguatezza per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        // Preparazione richiesta al Provider Gateway
        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId, req);

        // Chiamata al Provider Gateway
        JsonNode response = callProviderGateway(pgwRequest, "postQuestionarioAdeguatezza");

        // Recupero e aggiornamento dell'ordine con più campi
        JsonNode order = orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode updatedOrder = jsonManipulationService.updateOrderWithMultipleProviderFields(
            order, response, "codiceQuestionario", "codiceVersioneQuestionario");

        // Salvataggio dell'ordine aggiornato
        orderService.updateOrder(token, orderCode, updatedOrder);

        logger.infov("Invio questionario adeguatezza completato per orderCode: {0}", orderCode);
        return response;
    }

    /**
     * Verifica l'adeguatezza tramite il Provider Gateway
     * Esegue una sequenza di operazioni: verifica -> finalizzazione -> adeguata verifica
     */
    public JsonNode verificaAdeguatezza(String orderCode, String token, String sessionId) {
        logger.infov("Inizio verifica adeguatezza per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        // Preparazione richiesta al Provider Gateway
        ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);

        // Chiamata al Provider Gateway per verifica adeguatezza
        JsonNode response = callProviderGateway(pgwRequest, "verificaAdeguatezza");

        // Recupero e aggiornamento dell'ordine
        JsonNode order = orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode updatedOrder = jsonManipulationService.updateOrderWithProviderResponse(
            order, response, "adeguatezzaCliente");

        // Salvataggio dell'ordine aggiornato
        orderService.updateOrder(token, orderCode, updatedOrder);

        // Operazioni sequenziali richieste dal business
        finalizzazioneQuestionario(orderCode, token, sessionId);
        adeguataVerifica(orderCode, token, sessionId);

        logger.infov("Verifica adeguatezza completata per orderCode: {0}", orderCode);
        return response;
    }



    /**
     * Controlla lo stato di un'operazione tramite il Provider Gateway
     */
    public JsonNode checkOperazione(String orderCode, String token, String sessionId) {
        logger.infov("Inizio check operazione per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        try {
            ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
            JsonNode response = callProviderGateway(pgwRequest, "checkOperazione");

            logger.infov("Check operazione completato per orderCode: {0}", orderCode);
            return response;

        } catch (Exception e) {
            logger.errorv(e, "Errore durante il check operazione per orderCode: {0}", orderCode);
            throw e;
        }
    }

    /**
     * Finalizza il questionario tramite il Provider Gateway
     */
    public void finalizzazioneQuestionario(String orderCode, String token, String sessionId) {
        logger.infov("Inizio finalizzazione questionario per orderCode: {0}", orderCode);

        // Validazione input
        validationService.validateAllParameters(orderCode, token, sessionId);

        try {
            ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
            callProviderGateway(pgwRequest, "finalizzazioneQuestionario");

            logger.infov("Finalizzazione questionario completata per orderCode: {0}", orderCode);

        } catch (Exception e) {
            logger.errorv(e, "Errore durante la finalizzazione questionario per orderCode: {0}", orderCode);
            throw e;
        }
    }

    /**
     * Esegue l'adeguata verifica tramite il Provider Gateway
     */
    private void adeguataVerifica(String orderCode, String token, String sessionId) {
        logger.infov("Inizio adeguata verifica per orderCode: {0}", orderCode);

        try {
            ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
            callProviderGateway(pgwRequest, "adeguataVerifica");

            logger.infov("Adeguata verifica completata per orderCode: {0}", orderCode);

        } catch (Exception e) {
            logger.errorv(e, "Errore durante l'adeguata verifica per orderCode: {0}", orderCode);
            throw e;
        }
    }

    /**
     * Manda in firma a namirial i documenti recuperandoli dal provider gateway
     */
    public JsonNode firmaDocumenti(String orderCode, String token, String sessionId) {
        logger.infov("Inizio firma documenti per orderCode: {0}", orderCode);
        
        try {
            // Recupera i documenti dal provider gateway
            JsonNode documentsResponse = getDocumenti(orderCode, token, sessionId);
            logger.infov("Documenti recuperati per orderCode: {0}", orderCode);
            
            // Converte i documenti base64 in file temporanei
            List<File> documentFiles = utility.convertBase64ToFiles(documentsResponse, orderCode);
            logger.infov("Convertiti {0} documenti in file per orderCode: {1}", documentFiles.size(), orderCode);
            
            // Chiama il servizio di upload file
            String uploadResponse = fileUploaderClient.uploadFiles(token, documentFiles, "italiana-gs-new", orderCode, "sign");
            logger.infov("Upload documenti completato per orderCode: {0}, response: {1}", orderCode, uploadResponse);
            
            // Cleanup dei file temporanei
            utility.cleanupTempFiles(documentFiles);

            // Parsa e restituisce la risposta
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readTree(uploadResponse);
            
        } catch (Exception e) {
            logger.errorv(e, "Errore durante la firma documenti per orderCode: {0}", orderCode);
            throw new RuntimeException("Errore durante l'upload dei documenti per la firma", e);
        }
    }

    public JsonNode getDocumenti(String orderCode, String token, String sessionId) {
        logger.infov("Inizio recupero documenti per orderCode: {0}", orderCode);
        try {
            ObjectNode pgwRequest = utility.stdReqToPg(orderCode, token, sessionId);
            JsonNode response = callProviderGateway(pgwRequest, "generazioneDocumenti");
            logger.infov("Recupero documenti completato per orderCode: {0}", orderCode);
            return response;
        } catch (Exception e) {
            logger.errorv(e, "Errore durante il recupero documenti per orderCode: {0}", orderCode);
            throw e;
        }
    }
}
