package be.digitech.italianautility.service.v1.utilityService;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import jakarta.ws.rs.core.MediaType;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.resteasy.reactive.server.multipart.MultipartFormDataOutput;

@ApplicationScoped
public class Utility {

    @Inject
    ServiceOrder orderService;

    @Inject
    ServiceCustomer customerService;

    @Inject
    ServiceProduct productService;

    @Inject
    Logger logger;

    public ObjectNode stdReqToPg(String orderCode, String token, String sessionId) {
        ObjectNode reqToPg= JsonNodeFactory.instance.objectNode(), data=JsonNodeFactory.instance.objectNode();
        JsonNode order=orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode customer=customerService.findById(token, order.get("data").get("customerId").asLong());
        JsonNode product=productService.findById(token, order.get("data").get("productId").asLong());
        data.set("order", order);
        data.set("customer", customer);
        data.set("product", product);
        reqToPg.set("data", data);
        return reqToPg;
    }

    public ObjectNode stdReqToPg(String orderCode, String token, String sessionId, JsonNode req) {
        ObjectNode reqToPg= JsonNodeFactory.instance.objectNode(), data=JsonNodeFactory.instance.objectNode();
        JsonNode order=orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode customer=customerService.findById(token, order.get("data").get("customerId").asLong());
        JsonNode product=productService.findById(token, order.get("data").get("productId").asLong());
        data.set("order", order);
        data.set("customer", customer);
        data.set("product", product);
        data.set("domande", req);
        reqToPg.set("data", data);
        return reqToPg;
    }

    /**
     * Crea un ClientMultipartForm direttamente dai documenti base64 senza creare file temporanei
     */
    public ClientMultipartForm createMultipartFormFromBase64(JsonNode documentsResponse, String orderCode) throws Exception {
        // Verifica la struttura della risposta
        if (!documentsResponse.has("documentsToSign")) {
            throw new IllegalArgumentException("Risposta del provider gateway non contiene documenti validi");
        }

        JsonNode documenti = documentsResponse.get("documentsToSign");
        ClientMultipartForm form = ClientMultipartForm.create();

        // Processa proposta
        if (documenti.has("proposta") && !documenti.get("proposta").isNull()) {
            String base64Content = documenti.get("proposta").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_proposta.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent),  MediaType.APPLICATION_OCTET_STREAM);
            logger.infov("Aggiunto documento proposta al multipart form: {0}", fileName);
        }

        // Processa privacy
        if (documenti.has("privacy") && !documenti.get("privacy").isNull()) {
            String base64Content = documenti.get("privacy").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_privacy.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent), MediaType.APPLICATION_OCTET_STREAM);
            logger.infov("Aggiunto documento privacy al multipart form: {0}", fileName);
        }

        // Processa allegato_h
        if (documenti.has("allegato_h") && !documenti.get("allegato_h").isNull()) {
            String base64Content = documenti.get("allegato_h").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_allegato_h.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent), MediaType.APPLICATION_OCTET_STREAM);
            logger.infov("Aggiunto documento allegato_h al multipart form: {0}", fileName);
        }

        return form;
    }

    /**
     * Crea file temporanei direttamente dai documenti base64 (versione ottimizzata)
     */
    public List<File> createTempFilesFromBase64(JsonNode documentsResponse, String orderCode) throws Exception {
        List<File> files = new ArrayList<>();

        // Verifica la struttura della risposta
        if (!documentsResponse.has("documentsToSign")) {
            throw new IllegalArgumentException("Risposta del provider gateway non contiene documenti validi");
        }

        JsonNode documenti = documentsResponse.get("documentsToSign");

        // Processa proposta
        if (documenti.has("proposta") && !documenti.get("proposta").isNull()) {
            String base64Content = documenti.get("proposta").asText();
            File propostaFile = createTempFileFromBase64(base64Content, orderCode + "_proposta", ".pdf");
            files.add(propostaFile);
            logger.infov("Creato file proposta: {0}", propostaFile.getName());
        }

        // Processa privacy
        if (documenti.has("privacy") && !documenti.get("privacy").isNull()) {
            String base64Content = documenti.get("privacy").asText();
            File privacyFile = createTempFileFromBase64(base64Content, orderCode + "_privacy", ".pdf");
            files.add(privacyFile);
            logger.infov("Creato file privacy: {0}", privacyFile.getName());
        }

        // Processa allegato_h
        if (documenti.has("allegato_h") && !documenti.get("allegato_h").isNull()) {
            String base64Content = documenti.get("allegato_h").asText();
            File allegatoFile = createTempFileFromBase64(base64Content, orderCode + "_allegato_h", ".pdf");
            files.add(allegatoFile);
            logger.infov("Creato file allegato_h: {0}", allegatoFile.getName());
        }

        if (files.isEmpty()) {
            throw new IllegalStateException("Nessun documento valido trovato nella risposta del provider gateway");
        }

        return files;
    }

    /**
     * Crea un MultipartFormDataOutput direttamente dai documenti base64
     */
    public MultipartFormDataOutput createMultipartFormDataFromBase64(JsonNode documentsResponse, String orderCode) throws Exception {
        // Verifica la struttura della risposta
        if (!documentsResponse.has("documentsToSign")) {
            throw new IllegalArgumentException("Risposta del provider gateway non contiene documenti validi");
        }

        JsonNode documenti = documentsResponse.get("documentsToSign");
        MultipartFormDataOutput multipartOutput = new MultipartFormDataOutput();

        // Processa proposta
        if (documenti.has("proposta") && !documenti.get("proposta").isNull()) {
            String base64Content = documenti.get("proposta").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_proposta.pdf";
            multipartOutput.addFormData("documents", new ByteArrayInputStream(fileContent),
                MediaType.APPLICATION_OCTET_STREAM_TYPE, fileName);
            logger.infov("Aggiunto documento proposta al multipart: {0}", fileName);
        }

        // Processa privacy
        if (documenti.has("privacy") && !documenti.get("privacy").isNull()) {
            String base64Content = documenti.get("privacy").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_privacy.pdf";
            multipartOutput.addFormData("documents", new ByteArrayInputStream(fileContent),
                MediaType.APPLICATION_OCTET_STREAM_TYPE, fileName);
            logger.infov("Aggiunto documento privacy al multipart: {0}", fileName);
        }

        // Processa allegato_h
        if (documenti.has("allegato_h") && !documenti.get("allegato_h").isNull()) {
            String base64Content = documenti.get("allegato_h").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_allegato_h.pdf";
            multipartOutput.addFormData("documents", new ByteArrayInputStream(fileContent),
                MediaType.APPLICATION_OCTET_STREAM_TYPE, fileName);
            logger.infov("Aggiunto documento allegato_h al multipart: {0}", fileName);
        }

        return multipartOutput;
    }

    /**
     * Decodifica il contenuto base64 rimuovendo eventuali prefissi data URL
     */
    private byte[] decodeBase64Content(String base64Content) {
        // Rimuove il prefixo data URL se presente
        String cleanBase64 = base64Content;
        if (base64Content.contains(",")) {
            cleanBase64 = base64Content.split(",")[1];
        }

        // Decodifica il contenuto base64
        return Base64.getDecoder().decode(cleanBase64);
    }

    /**
     * Crea un file temporaneo da contenuto base64
     */
    public File createTempFileFromBase64(String base64Content, String fileName, String extension) throws Exception {
        // Rimuove il prefixo data URL se presente
        String cleanBase64 = base64Content;
        if (base64Content.contains(",")) {
            cleanBase64 = base64Content.split(",")[1];
        }

        // Decodifica il contenuto base64
        byte[] fileContent = Base64.getDecoder().decode(cleanBase64);

        // Crea file temporaneo
        File tempFile = File.createTempFile(fileName, extension);
        tempFile.deleteOnExit(); // Assicura la pulizia in caso di shutdown dell'applicazione

        // Scrive il contenuto nel file
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(fileContent);
            fos.flush();
        }

        return tempFile;
    }

    /**
     * Pulisce i file temporanei creati
     */
    public void cleanupTempFiles(List<File> files) {
        for (File file : files) {
            try {
                if (file.exists() && !file.delete()) {
                    logger.warnv("Impossibile eliminare il file temporaneo: {0}", file.getAbsolutePath());
                }
            } catch (Exception e) {
                logger.warnv(e, "Errore durante l'eliminazione del file temporaneo: {0}", file.getAbsolutePath());
            }
        }
    }
}
