package be.digitech.italianautility.service.v1.utilityService;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import io.vertx.core.buffer.Buffer;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.resteasy.reactive.client.api.ClientMultipartForm;

@ApplicationScoped
public class Utility {

    @Inject
    ServiceOrder orderService;

    @Inject
    ServiceCustomer customerService;

    @Inject
    ServiceProduct productService;

    @Inject
    Logger logger;

    public ObjectNode stdReqToPg(String orderCode, String token, String sessionId) {
        ObjectNode reqToPg= JsonNodeFactory.instance.objectNode(), data=JsonNodeFactory.instance.objectNode();
        JsonNode order=orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode customer=customerService.findById(token, order.get("data").get("customerId").asLong());
        JsonNode product=productService.findById(token, order.get("data").get("productId").asLong());
        data.set("order", order);
        data.set("customer", customer);
        data.set("product", product);
        reqToPg.set("data", data);
        return reqToPg;
    }

    public ObjectNode stdReqToPg(String orderCode, String token, String sessionId, JsonNode req) {
        ObjectNode reqToPg= JsonNodeFactory.instance.objectNode(), data=JsonNodeFactory.instance.objectNode();
        JsonNode order=orderService.findByOrderCode(token, orderCode, sessionId);
        JsonNode customer=customerService.findById(token, order.get("data").get("customerId").asLong());
        JsonNode product=productService.findById(token, order.get("data").get("productId").asLong());
        data.set("order", order);
        data.set("customer", customer);
        data.set("product", product);
        data.set("domande", req);
        reqToPg.set("data", data);
        return reqToPg;
    }

    /**
     * Crea un ClientMultipartForm direttamente dai documenti base64 senza creare file temporanei
     */
    public ClientMultipartForm createMultipartFormFromBase64(JsonNode documentsResponse, String orderCode) throws Exception {
        // Verifica la struttura della risposta
        if (!documentsResponse.has("documentsToSign")) {
            throw new IllegalArgumentException("Risposta del provider gateway non contiene documenti validi");
        }

        JsonNode documenti = documentsResponse.get("documentsToSign");
        ClientMultipartForm form = ClientMultipartForm.create();

        // Processa proposta
        if (documenti.has("proposta") && !documenti.get("proposta").isNull()) {
            String base64Content = documenti.get("proposta").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_proposta.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent), "application/pdf");
            logger.infov("Aggiunto documento proposta al multipart form: {0}", fileName);
        }

        // Processa privacy
        if (documenti.has("privacy") && !documenti.get("privacy").isNull()) {
            String base64Content = documenti.get("privacy").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_privacy.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent), "application/pdf");
            logger.infov("Aggiunto documento privacy al multipart form: {0}", fileName);
        }

        // Processa allegato_h
        if (documenti.has("allegato_h") && !documenti.get("allegato_h").isNull()) {
            String base64Content = documenti.get("allegato_h").asText();
            byte[] fileContent = decodeBase64Content(base64Content);
            String fileName = orderCode + "_allegato_h.pdf";
            form.binaryFileUpload("documents", fileName, Buffer.buffer(fileContent), "application/pdf");
            logger.infov("Aggiunto documento allegato_h al multipart form: {0}", fileName);
        }

        return form;
    }

    /**
     * Decodifica il contenuto base64 rimuovendo eventuali prefissi data URL
     */
    private byte[] decodeBase64Content(String base64Content) {
        // Rimuove il prefixo data URL se presente
        String cleanBase64 = base64Content;
        if (base64Content.contains(",")) {
            cleanBase64 = base64Content.split(",")[1];
        }

        // Decodifica il contenuto base64
        return Base64.getDecoder().decode(cleanBase64);
    }
}
