package be.digitech.italianautility.client;

import io.quarkus.rest.client.reactive.NotBody;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.ClientHeaderParam;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestForm;

import java.io.File;
import java.util.List;

@RegisterRestClient(configKey = "file-uploader")
public interface FileUploaderClient {

    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    @ClientHeaderParam(name = "Authorization", value = "{token}")
    @Path("/upload")
    String uploadFiles(
            @NotBody String token,
            @RestForm("documents") List<File> documents,
                       @QueryParam("product-code") String productCode,
                       @QueryParam("order_code") String orderCode,
                       @QueryParam("action") String action
            );


}
